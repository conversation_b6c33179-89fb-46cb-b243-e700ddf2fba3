import threading
from typing import Dict, List, Any, Union
from intelligent_executor.vectordb.milvus_lite_client.milvus_client import MilvusLiteClient
from services.testcase_service import TestcaseService
from log.logger import log_info, log_error, log_warning, log_debug


class VectorTestcaseService:
    """Vector testcase service for business logic"""
    
    def __init__(self):
        self.testcase_service = TestcaseService()
        self.client = MilvusLiteClient()
        
    def check_milvus_service_health(self) -> bool:
        """Check Milvus service health"""
        try:
            with MilvusLiteClient() as client:
                response = client.get('/health')
                return response.get('status') == 'healthy'
        except Exception as e:
            log_error(f"Milvus health check failed: {e}")
            return False
    
    def get_vector_db_status(self) -> Dict[str, Any]:
        """Get vector database status"""
        try:
            with MilvusLiteClient() as client:
                return client.get('/status')
        except Exception as e:
            log_error(f"Get vector db status failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def insert_testcase(self, testcase_data: Union[Dict, List[Dict]]) -> Dict:
        try:
            data = {
                "testcase_data": testcase_data
            }
            with MilvusLiteClient() as client:
                return client.post('/insert_testcase', data)
        except Exception as e:
            log_error(f"Insert testcase failed: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def batch_insert_testcases(self, testcases: List[Dict], batch_size: int = 100) -> Dict:
        try:
            data = {
                "testcases": testcases,
                "batch_size": batch_size
            }
            with MilvusLiteClient() as client:
                return client.post('/batch_insert_testcases', data)
        except Exception as e:
            log_error(f"Batch insert testcases failed: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def search_testcase(self, query_text: str, top_k: int = 10) -> Dict:
        try:
            data = {
                "query_text": query_text,
                "top_k": top_k
            }
            with MilvusLiteClient() as client:
                return client.post('/search_testcase', data)
        except Exception as e:
            log_error(f"Search testcase failed: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def delete_testcase(self, testcase_ids: List[str]) -> Dict:
        try:
            data = {
                "testcase_ids": testcase_ids
            }
            with MilvusLiteClient() as client:
                return client.post('/delete_testcase', data)
        except Exception as e:
            log_error(f"Delete testcase failed: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def search_similar_testcases(self, query: str, top_k: int = 10, score_threshold: float = 0.7) -> List[Dict]:
        try:
            search_result = self.search_testcase(query, top_k)
            
            if search_result.get('status') != 'success':
                log_error(f"Search failed: {search_result}")
                return []
            
            results = []
            for item in search_result.get('results', []):
                score = item.get('score', 0)
                # Convert distance to similarity (0-1 range for cosine distance)
                similarity = 1 - (score / 2)
                
                if similarity >= score_threshold:
                    item['similarity'] = similarity
                    results.append(item)
            
            results.sort(key=lambda x: x['similarity'], reverse=True)
            return results
            
        except Exception as e:
            log_error(f"Search similar testcases failed: {e}")
            return []
    
    def update_testcase(self, testcase_data: Dict) -> bool:
        """Update testcase by delete and insert"""
        try:
            testcase_id = testcase_data.get('id') or testcase_data.get('Testcase_Number')
            if not testcase_id:
                log_error("Missing testcase ID")
                return False
            
            delete_result = self.delete_testcase([str(testcase_id)])
            if delete_result.get('status') != 'success':
                log_warning(f"Delete old testcase failed: {delete_result}")
            
            insert_result = self.insert_testcase(testcase_data)
            return insert_result.get('status') == 'success'
            
        except Exception as e:
            log_error(f"Update testcase failed: {e}")
            return False
    
    def delete_testcases(self, testcase_ids: List[str]) -> bool:
        try:
            result = self.delete_testcase(testcase_ids)
            return result.get('status') == 'success'
        except Exception as e:
            log_error(f"Delete testcases failed: {e}")
            return False

    def clear_vector_collection(self) -> bool:
        """Clear all data in vector collection"""
        try:
            with MilvusLiteClient() as client:
                response = client.post('/clear_collection', {})
                return response.get('status') == 'success'
        except Exception as e:
            log_error(f"Clear vector collection failed: {e}")
            return False

    def ensure_collection_exists(self) -> bool:
        """Ensure collection exists by trying to clear it (which will create if not exists)"""
        try:
            # This will create the collection if it doesn't exist
            self.clear_vector_collection()
            log_info("Vector collection initialized successfully")
            return True
        except Exception as e:
            log_error(f"Failed to ensure collection exists: {e}")
            return False

    def batch_insert_with_retry(self, testcases: List[Dict], batch_size: int = 100, max_retries: int = 3) -> Dict:
        """Batch insert with retry mechanism for first-time collection creation"""
        for attempt in range(max_retries):
            try:
                result = self.batch_insert_testcases(testcases, batch_size)
                if result.get('status') == 'success':
                    return result
                else:
                    log_warning(f"Batch insert attempt {attempt + 1} failed: {result.get('message', 'Unknown error')}")
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(1)  # Wait 1 second before retry
            except Exception as e:
                log_error(f"Batch insert attempt {attempt + 1} exception: {e}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(1)  # Wait 1 second before retry
                else:
                    return {
                        'status': 'error',
                        'message': str(e)
                    }

        return {
            'status': 'error',
            'message': f'Failed after {max_retries} attempts'
        }

    def sync_testcases_to_vector_db(self, batch_size: int = 100, clear_before_sync: bool = True) -> Dict[str, Any]:
        """Sync testcases to vector database"""
        try:
            if not self.check_milvus_service_health():
                return {
                    'success': False,
                    'total_synced': 0,
                    'error': 'Milvus service unavailable'
                }

            # Ensure collection exists and optionally clear data
            if clear_before_sync:
                log_info("Initializing vector collection and clearing existing data...")
                if not self.clear_vector_collection():
                    log_warning("Failed to clear existing vector data, trying to ensure collection exists...")
                    if not self.ensure_collection_exists():
                        log_error("Failed to initialize vector collection")
                        return {
                            'success': False,
                            'total_synced': 0,
                            'error': 'Failed to initialize vector collection'
                        }
                else:
                    log_info("Successfully cleared existing vector data")
            else:
                # Just ensure collection exists without clearing
                log_info("Ensuring vector collection exists...")
                if not self.ensure_collection_exists():
                    log_error("Failed to initialize vector collection")
                    return {
                        'success': False,
                        'total_synced': 0,
                        'error': 'Failed to initialize vector collection'
                    }

            testcases_result = self.testcase_service.get_all_testcases(page=1, page_size=1)
            total_count = testcases_result.get('total', 0)
            
            if total_count == 0:
                log_warning("No testcase data found")
                return {
                    'success': True,
                    'total_synced': 0,
                    'message': 'No testcase data to sync'
                }
            
            log_info(f"Batch sync started for {total_count} test cases.")
            
            total_synced = 0
            page = 1
            page_size = batch_size
            
            while True:
                testcases_result = self.testcase_service.get_all_testcases(
                    page=page, 
                    page_size=page_size
                )
                testcases = testcases_result.get('data', [])
                
                if not testcases:
                    break
                
                try:
                    # Use retry mechanism for batch insert, especially important for first batch
                    result = self.batch_insert_with_retry(testcases, batch_size, max_retries=3)

                    if result.get('status') == 'success':
                        synced_count = result.get('total_inserted', 0)
                        total_synced += synced_count
                        log_debug(f"Page {page} synced: {synced_count} testcases")
                    else:
                        log_error(f"Page {page} sync failed: {result.get('message', 'Unknown error')}")

                except Exception as e:
                    log_error(f"Page {page} sync exception: {e}")
                
                page += 1
                
                if len(testcases) < page_size:
                    break
            
            log_info(f"Testcase sync completed: {total_synced} testcases")
            
            return {
                'success': True,
                'total_synced': total_synced,
                'total_count': total_count,
                'message': f'Successfully synced {total_synced} testcases'
            }
            
        except Exception as e:
            log_error(f"Sync testcases to vector db failed: {e}")
            return {
                'success': False,
                'total_synced': 0,
                'error': str(e)
            }
    
    def async_sync_testcases(self, batch_size: int = 100, clear_before_sync: bool = True) -> None:
        """Async sync testcases in background thread"""
        def sync_worker():
            try:
                result = self.sync_testcases_to_vector_db(batch_size, clear_before_sync)
                if result['success']:
                    log_info(f"Background sync completed: {result['message']}")
                else:
                    log_error(f"Background sync failed: {result.get('error', 'Unknown error')}")
            except Exception as e:
                log_error(f"Background sync exception: {e}")

        sync_thread = threading.Thread(target=sync_worker, daemon=True)
        sync_thread.start()
        log_info("Background testcase sync task started...")
    
    def get_testcase_recommendations(self, query: str, top_k: int = 5) -> List[Dict]:
        try:
            return self.search_similar_testcases(query, top_k, score_threshold=0.3)
        except Exception as e:
            log_error(f"Get testcase recommendations failed: {e}")
            return []

    def fix_vector_index(self) -> Dict:
        """Fix missing vector index"""
        try:
            with MilvusLiteClient() as client:
                return client.post('/fix_index', {})
        except Exception as e:
            log_error(f"Fix vector index failed: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

    def recreate_collection(self) -> Dict:
        """Recreate collection with proper index"""
        try:
            with MilvusLiteClient() as client:
                return client.post('/recreate_collection', {})
        except Exception as e:
            log_error(f"Recreate collection failed: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

    def search_testcases_with_complete_details(self, query: str, top_k: int = 10, score_threshold: float = 0.5) -> List[Dict]:
        """Search testcases and return complete details from database"""
        try:
            # 首先从向量数据库搜索相似测试用例
            vector_results = self.search_similar_testcases(query, top_k, score_threshold)

            if not vector_results:
                log_info(f"No similar testcases found for query: {query}")
                return []

            # 提取测试用例ID并从数据库获取完整信息
            complete_results = []
            for vector_result in vector_results:
                testcase_id = vector_result.get('id')
                similarity = vector_result.get('similarity', 0)

                if testcase_id:
                    # 根据ID从数据库获取完整测试用例信息
                    testcase_detail = self.testcase_service.get_testcase_by_number(str(testcase_id))

                    if testcase_detail:
                        # 合并向量搜索结果和数据库详情
                        complete_result = {
                            **testcase_detail,  # 完整的测试用例数据
                            'similarity': similarity,  # 相似度
                            'vector_score': vector_result.get('score', 0)  # 原始向量距离分数
                        }
                        complete_results.append(complete_result)
                    else:
                        log_warning(f"Testcase with ID {testcase_id} not found in database")

            # 按相似度排序
            complete_results.sort(key=lambda x: x['similarity'], reverse=True)

            log_info(f"Found {len(complete_results)} complete testcase details for query: {query}")
            return complete_results

        except Exception as e:
            log_error(f"Search testcases with complete details failed: {e}")
            return []